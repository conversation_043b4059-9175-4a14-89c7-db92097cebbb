2025-05-27 20:57:03,893 - INFO - ============================================================
2025-05-27 20:57:03,908 - INFO - ----------------------------------------
2025-05-27 20:57:03,928 - INFO - Found 6 images in C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:57:03,930 - INFO - Unified Video Processor initialized
2025-05-27 20:57:03,937 - INFO - Images directory: C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:57:03,937 - INFO - Available images: 6
2025-05-27 20:57:03,938 - INFO - Output directory: unified_outputs
2025-05-27 20:57:04,179 - INFO - ----------------------------------------
2025-05-27 20:57:04,196 - INFO - Found 6 images in C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:57:04,197 - INFO - Unified Video Processor initialized
2025-05-27 20:57:04,230 - INFO - Images directory: C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:57:04,239 - INFO - Available images: 6
2025-05-27 20:57:04,240 - INFO - Output directory: unified_outputs
2025-05-27 20:57:04,387 - WARNING - Could not parse dimension 'invalid', using default 100px
2025-05-27 20:57:04,438 - INFO - ----------------------------------------
2025-05-27 20:57:04,458 - INFO - Found 6 images in C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:57:04,458 - INFO - Unified Video Processor initialized
2025-05-27 20:57:04,458 - INFO - Images directory: C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:57:04,459 - INFO - Available images: 6
2025-05-27 20:57:04,459 - INFO - Output directory: unified_outputs
2025-05-27 20:57:04,517 - WARNING - Could not resolve image source 'nonexistent.jpg', using random image
2025-05-27 20:57:04,528 - INFO - Available images: 6
2025-05-27 20:57:04,575 - INFO - ----------------------------------------
2025-05-27 20:57:04,594 - INFO - Found 6 images in C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:57:04,594 - INFO - Unified Video Processor initialized
2025-05-27 20:57:04,602 - INFO - Images directory: C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:57:04,602 - INFO - Available images: 6
2025-05-27 20:57:04,603 - INFO - Output directory: test_outputs
2025-05-27 20:57:04,740 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 20:57:05,204 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 20:57:05,824 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 20:57:06,437 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 20:57:08,392 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 20:57:08,963 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 20:57:09,539 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 20:57:09,591 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 20:59:59,957 - INFO - ----------------------------------------
2025-05-27 20:59:59,962 - INFO - Found 6 images in C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:59:59,962 - INFO - Unified Video Processor initialized
2025-05-27 20:59:59,962 - INFO - Images directory: C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 20:59:59,963 - INFO - Available images: 6
2025-05-27 20:59:59,963 - INFO - Output directory: batch_test_outputs
2025-05-27 20:59:59,979 - INFO - 
============================================================
2025-05-27 20:59:59,992 - INFO - ============================================================
2025-05-27 21:00:00,159 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:00:00,395 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:00:00,676 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:00:01,130 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:00:03,178 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:00:03,577 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:00:04,040 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:00:04,097 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:01:26,238 - INFO - Found 6 images in C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 21:01:26,239 - INFO - Unified Video Processor initialized
2025-05-27 21:01:26,240 - INFO - Images directory: C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140
2025-05-27 21:01:26,240 - INFO - Available images: 6
2025-05-27 21:01:26,241 - INFO - Output directory: unified_outputs
2025-05-27 21:01:26,345 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:01:26,505 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:01:26,640 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:01:26,910 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:01:27,050 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:01:27,241 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:01:27,403 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
2025-05-27 21:01:27,430 - ERROR - Failed to create text clip: expected str, bytes or os.PathLike object, not tuple
