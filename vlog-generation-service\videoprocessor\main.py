#!/usr/bin/env python3
"""
VisionFrame AI Video Processor - Main Runner
Complete video generation from JSON templates
"""

import json
import sys
import os
import logging
from pathlib import Path
from typing import Dict, List, Any

# Import our processors
from video_processor import VideoProcessor
from text_processor import TextProcessor, TranscriptProcessor
from audio_processor import AudioProcessor, TranscriptExtractor

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('video_processing.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class VisionFrameVideoProcessor:
    """Complete video processor that handles all aspects of video generation"""

    def __init__(self, elevenlabs_api_key: str = None, output_dir: str = "output"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # Initialize processors
        self.video_processor = VideoProcessor(str(self.output_dir))
        self.text_processor = TextProcessor()
        self.audio_processor = AudioProcessor(elevenlabs_api_key)
        self.transcript_processor = TranscriptProcessor()
        self.transcript_extractor = TranscriptExtractor()

        logger.info("VisionFrame Video Processor initialized")

    def process_template(self, template_path: str, output_filename: str = None) -> str:
        """Process complete video template"""
        logger.info(f"Processing template: {template_path}")

        # Load template with UTF-8 encoding
        with open(template_path, 'r', encoding='utf-8') as f:
            template_data = json.load(f)

        # Validate template
        self.validate_template(template_data)

        # Pre-process template (extract transcripts, generate audio, etc.)
        processed_template = self.preprocess_template(template_data)

        # Generate video
        output_path = self.video_processor.process_template(processed_template, output_filename)

        # Cleanup
        self.cleanup()

        logger.info(f"Video processing complete: {output_path}")
        return output_path

    def validate_template(self, template_data: Dict[str, Any]):
        """Validate template structure"""
        required_fields = ['output_format', 'width', 'height', 'elements']

        for field in required_fields:
            if field not in template_data:
                raise ValueError(f"Missing required field: {field}")

        if not template_data['elements']:
            raise ValueError("Template must contain at least one element")

        logger.info("Template validation passed")

    def preprocess_template(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """Preprocess template to extract transcripts and generate audio"""
        logger.info("Preprocessing template...")

        processed_template = template_data.copy()

        # Process each scene
        for element in processed_template.get('elements', []):
            if element.get('type') == 'composition':
                self.preprocess_scene(element)

        return processed_template

    def preprocess_scene(self, scene_element: Dict[str, Any]):
        """Preprocess a single scene"""
        scene_name = scene_element.get('name', 'Unknown Scene')
        logger.info(f"Preprocessing scene: {scene_name}")

        elements = scene_element.get('elements', [])

        # Find audio and text elements
        audio_elements = [e for e in elements if e.get('type') == 'audio']
        text_elements = [e for e in elements if e.get('type') == 'text']

        # Process audio elements and extract transcripts
        for audio_element in audio_elements:
            transcript_text = self.transcript_extractor.extract_from_audio_element(audio_element)

            # Clean transcript
            transcript_text = self.transcript_extractor.clean_transcript_text(transcript_text)

            # Store transcript in audio element
            audio_element['transcript'] = transcript_text

            # Generate audio if needed
            if not audio_element.get('source') or not os.path.exists(audio_element.get('source', '')):
                logger.info(f"Generating audio for: {audio_element.get('name', 'Unknown')}")
                audio_clip, duration = self.audio_processor.process_audio_element(audio_element, transcript_text)

                if audio_clip:
                    # Save generated audio
                    audio_filename = f"{scene_name}_{audio_element.get('name', 'audio')}.mp3"
                    audio_path = self.output_dir / audio_filename
                    audio_clip.write_audiofile(str(audio_path))
                    audio_clip.close()

                    # Update source in element
                    audio_element['source'] = str(audio_path)
                    audio_element['duration'] = duration

                    logger.info(f"Generated audio saved: {audio_path}")

        # Link text elements with their transcript sources
        for text_element in text_elements:
            transcript_source_id = text_element.get('transcript_source')

            if transcript_source_id:
                # Find corresponding audio element
                audio_element = next((e for e in audio_elements if e.get('id') == transcript_source_id), None)

                if audio_element and 'transcript' in audio_element:
                    # Link transcript to text element
                    text_element['transcript_text'] = audio_element['transcript']

                    # Apply maximum length constraint
                    max_length = text_element.get('transcript_maximum_length', 35)
                    if len(audio_element['transcript']) > max_length:
                        # Split into segments
                        segments = self.transcript_extractor.split_into_segments(
                            audio_element['transcript'], max_length
                        )
                        text_element['transcript_segments'] = segments
                        text_element['transcript_text'] = segments[0] if segments else audio_element['transcript']

    def cleanup(self):
        """Cleanup temporary files"""
        try:
            self.audio_processor.cleanup_temp_files()
            logger.info("Cleanup completed")
        except Exception as e:
            logger.warning(f"Cleanup failed: {e}")

def main():
    """Main function for command line usage"""
    if len(sys.argv) < 2:
        print("Usage: python main.py <template.json> [output_filename] [--elevenlabs-key KEY]")
        print("\nExample:")
        print("  python main.py sample_template.json my_video.mp4")
        print("  python main.py sample_template.json --elevenlabs-key your_api_key")
        sys.exit(1)

    template_file = sys.argv[1]
    output_filename = None
    elevenlabs_key = None

    # Parse arguments
    i = 2
    while i < len(sys.argv):
        if sys.argv[i] == '--elevenlabs-key' and i + 1 < len(sys.argv):
            elevenlabs_key = sys.argv[i + 1]
            i += 2
        else:
            if not output_filename:
                output_filename = sys.argv[i]
            i += 1

    # Check if template file exists
    if not os.path.exists(template_file):
        print(f"Error: Template file '{template_file}' not found")
        sys.exit(1)

    try:
        # Initialize processor
        processor = VisionFrameVideoProcessor(elevenlabs_key)

        # Process video
        output_path = processor.process_template(template_file, output_filename)

        print(f"\n✅ Video created successfully!")
        print(f"📁 Output: {output_path}")
        print(f"📊 Check video_processing.log for detailed logs")

    except Exception as e:
        logger.error(f"Video processing failed: {e}")
        print(f"\n❌ Error: {e}")
        print(f"📊 Check video_processing.log for detailed error information")
        sys.exit(1)

def create_sample_video():
    """Create a sample video using the provided template"""
    sample_template = "sample_template.json"

    if not os.path.exists(sample_template):
        print(f"Error: {sample_template} not found")
        print("Please ensure the sample template file exists in the current directory")
        return

    print("🎬 Creating sample video...")

    try:
        processor = VisionFrameVideoProcessor()
        output_path = processor.process_template(sample_template, "sample_output.mp4")

        print(f"\n✅ Sample video created!")
        print(f"📁 Output: {output_path}")

    except Exception as e:
        print(f"\n❌ Failed to create sample video: {e}")

if __name__ == "__main__":
    if len(sys.argv) == 2 and sys.argv[1] == "--sample":
        create_sample_video()
    else:
        main()
