#!/usr/bin/env python3
"""
Test script for the Unified Video Processor
Demonstrates processing templates with automatic image sourcing and Runway API speech generation
"""

import asyncio
import os
import sys
from pathlib import Path
import logging

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from unified_video_processor import UnifiedVideoProcessor, process_all_templates

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_single_template():
    """Test processing a single template"""
    
    logger.info("🧪 Testing single template processing...")
    
    # Initialize processor
    processor = UnifiedVideoProcessor(
        images_dir=r"C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140",
        runway_api_key=os.getenv('RUNWAY_API_KEY'),  # Optional
        output_dir="test_outputs"
    )
    
    # Test with a simple template
    template_path = "templates/chat promotion alternative.json"
    
    if not Path(template_path).exists():
        logger.error(f"Template not found: {template_path}")
        return False
    
    try:
        output_path = await processor.process_template(template_path)
        logger.info(f"✅ Single template test successful: {output_path}")
        return True
    except Exception as e:
        logger.error(f"❌ Single template test failed: {e}")
        return False
    finally:
        processor.cleanup()

async def test_batch_processing():
    """Test batch processing of all templates"""
    
    logger.info("🧪 Testing batch processing...")
    
    # Initialize processor
    processor = UnifiedVideoProcessor(
        images_dir=r"C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140",
        runway_api_key=os.getenv('RUNWAY_API_KEY'),  # Optional
        output_dir="batch_test_outputs"
    )
    
    try:
        results = await process_all_templates(processor, "templates")
        
        successful = [r for r in results if r['status'] == 'success']
        failed = [r for r in results if r['status'] == 'failed']
        
        logger.info(f"✅ Batch processing completed:")
        logger.info(f"   - Successful: {len(successful)}")
        logger.info(f"   - Failed: {len(failed)}")
        
        return len(successful) > 0
        
    except Exception as e:
        logger.error(f"❌ Batch processing test failed: {e}")
        return False
    finally:
        processor.cleanup()

async def test_image_resolution():
    """Test image resolution and sourcing"""
    
    logger.info("🧪 Testing image resolution...")
    
    processor = UnifiedVideoProcessor(
        images_dir=r"C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140"
    )
    
    # Test different image source formats
    test_sources = [
        "",  # Should get random image
        "19.jpg",  # Direct filename
        "19",  # Filename without extension
        "0",  # Index
        "nonexistent.jpg",  # Should fallback to random
    ]
    
    for source in test_sources:
        resolved = processor.resolve_image_source(source)
        logger.info(f"Source '{source}' → {resolved}")
    
    logger.info(f"Available images: {len(processor.available_images)}")
    return True

async def test_color_parsing():
    """Test color parsing functionality"""
    
    logger.info("🧪 Testing color parsing...")
    
    processor = UnifiedVideoProcessor()
    
    # Test different color formats
    test_colors = [
        "#ffffff",
        "#000000",
        "rgba(255,255,255,1)",
        "rgba(0,0,0,0.5)",
        "ffffff",
        "invalid_color",
        "",
    ]
    
    for color in test_colors:
        rgb = processor.parse_color(color)
        logger.info(f"Color '{color}' → {rgb}")
    
    return True

async def test_dimension_parsing():
    """Test dimension parsing functionality"""
    
    logger.info("🧪 Testing dimension parsing...")
    
    processor = UnifiedVideoProcessor()
    
    # Test different dimension formats
    test_dimensions = [
        "100px",
        "50%",
        "10vmin",
        "100",
        100,
        "invalid",
    ]
    
    canvas_width, canvas_height = 720, 1280
    
    for dim in test_dimensions:
        parsed = processor.parse_dimension(dim, canvas_width, canvas_height)
        logger.info(f"Dimension '{dim}' → {parsed}px")
    
    return True

async def run_all_tests():
    """Run all tests"""
    
    logger.info("🚀 Starting Unified Video Processor Tests")
    logger.info("=" * 60)
    
    tests = [
        ("Color Parsing", test_color_parsing),
        ("Dimension Parsing", test_dimension_parsing),
        ("Image Resolution", test_image_resolution),
        ("Single Template", test_single_template),
        ("Batch Processing", test_batch_processing),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running test: {test_name}")
        logger.info("-" * 40)
        
        try:
            success = await test_func()
            results.append((test_name, success))
            
            if success:
                logger.info(f"✅ {test_name} test passed")
            else:
                logger.info(f"❌ {test_name} test failed")
                
        except Exception as e:
            logger.error(f"💥 {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Print summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 TEST SUMMARY")
    logger.info("=" * 60)
    
    passed = [r for r in results if r[1]]
    failed = [r for r in results if not r[1]]
    
    logger.info(f"✅ Passed: {len(passed)}/{len(results)}")
    logger.info(f"❌ Failed: {len(failed)}/{len(results)}")
    
    if failed:
        logger.info("\n💥 Failed tests:")
        for test_name, _ in failed:
            logger.info(f"  - {test_name}")
    
    return len(failed) == 0

if __name__ == "__main__":
    # Run all tests
    success = asyncio.run(run_all_tests())
    
    if success:
        logger.info("\n🎉 All tests passed!")
        sys.exit(0)
    else:
        logger.info("\n💥 Some tests failed!")
        sys.exit(1)
