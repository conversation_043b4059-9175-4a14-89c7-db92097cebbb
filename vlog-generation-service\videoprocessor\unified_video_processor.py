#!/usr/bin/env python3
"""
Unified Video Processor for VisionFrame AI
Handles all video templates with automatic image sourcing and Runway API speech generation
"""

import os
import sys
import json
import logging
import random
import requests
import tempfile
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union
from datetime import datetime
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import cv2

# Flexible MoviePy imports
try:
    from moviepy.editor import (
        VideoFileClip, ImageClip, TextClip, CompositeVideoClip,
        AudioFileClip, concatenate_videoclips, ColorClip
    )
    print("✅ Using MoviePy 1.x imports")
except ImportError:
    try:
        from moviepy.video.io.VideoFileClip import VideoFileClip
        from moviepy.video.io.ImageSequenceClip import ImageSequenceClip
        from moviepy.video.VideoClip import ImageClip, TextClip
        from moviepy.video.compositing.CompositeVideoClip import CompositeVideoClip
        from moviepy.audio.io.AudioFileClip import AudioFileClip
        from moviepy.video.compositing.concatenate import concatenate_videoclips
        from moviepy.video.VideoClip import ColorClip
        print("✅ Using MoviePy legacy imports")
    except ImportError:
        print("❌ MoviePy not available. Please install moviepy")
        sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('unified_video_processing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UnifiedVideoProcessor:
    """
    Unified video processor that handles all template types with:
    - Automatic image sourcing from specified directory
    - Runway API for speech generation
    - Template-agnostic processing
    - Comprehensive element support (text, image, shape, audio, composition)
    """

    def __init__(self,
                 images_dir: str = r"C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140",
                 runway_api_key: str = None,
                 output_dir: str = "unified_outputs"):
        """
        Initialize the unified video processor

        Args:
            images_dir: Directory containing images for templates
            runway_api_key: API key for Runway speech generation
            output_dir: Directory for output videos
        """
        self.images_dir = Path(images_dir)
        self.runway_api_key = runway_api_key or os.getenv('RUNWAY_API_KEY')
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        # Create temp directory for audio files
        self.temp_dir = Path(tempfile.gettempdir()) / "unified_video_processor"
        self.temp_dir.mkdir(exist_ok=True)

        # Cache for loaded images and fonts
        self.image_cache = {}
        self.font_cache = {}

        # Default settings
        self.default_font_size = 24
        self.default_font_color = "#ffffff"
        self.default_background_color = "#000000"

        # Load available images
        self.available_images = self._load_available_images()

        logger.info(f"Unified Video Processor initialized")
        logger.info(f"Images directory: {self.images_dir}")
        logger.info(f"Available images: {len(self.available_images)}")
        logger.info(f"Output directory: {self.output_dir}")

    def _load_available_images(self) -> List[Path]:
        """Load list of available images from the images directory"""
        if not self.images_dir.exists():
            logger.warning(f"Images directory not found: {self.images_dir}")
            return []

        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
        images = []

        for file_path in self.images_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                images.append(file_path)

        logger.info(f"Found {len(images)} images in {self.images_dir}")
        return images

    def get_random_image(self) -> Optional[Path]:
        """Get a random image from the available images"""
        if not self.available_images:
            logger.warning("No images available")
            return None
        return random.choice(self.available_images)

    def get_image_by_index(self, index: int) -> Optional[Path]:
        """Get image by index from available images"""
        if not self.available_images or index >= len(self.available_images):
            return self.get_random_image()
        return self.available_images[index]

    async def generate_speech_runway(self, text: str, voice_id: str = "default") -> Optional[str]:
        """
        Generate speech using Runway API

        Args:
            text: Text to convert to speech
            voice_id: Voice ID for speech generation

        Returns:
            Path to generated audio file or None if failed
        """
        if not self.runway_api_key:
            logger.warning("Runway API key not provided, skipping speech generation")
            return None

        try:
            # Try to import aiohttp
            import aiohttp
        except ImportError:
            logger.warning("aiohttp not available, cannot use Runway API")
            return None

        try:
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            audio_filename = f"speech_{timestamp}.mp3"
            audio_path = self.temp_dir / audio_filename

            # Runway API endpoint (replace with actual endpoint)
            url = "https://api.runwayml.com/v1/speech/generate"

            headers = {
                "Authorization": f"Bearer {self.runway_api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "text": text,
                "voice": voice_id,
                "format": "mp3",
                "speed": 1.0
            }

            logger.info(f"Generating speech for text: {text[:50]}...")

            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=payload) as response:
                    if response.status == 200:
                        audio_data = await response.read()
                        with open(audio_path, 'wb') as f:
                            f.write(audio_data)
                        logger.info(f"Speech generated successfully: {audio_path}")
                        return str(audio_path)
                    else:
                        error_text = await response.text()
                        logger.error(f"Runway API error: {response.status} - {error_text}")
                        return None

        except Exception as e:
            logger.error(f"Error generating speech with Runway API: {e}")
            return None

    def generate_speech_fallback(self, text: str) -> Optional[str]:
        """
        Fallback speech generation using system TTS or placeholder

        Args:
            text: Text to convert to speech

        Returns:
            Path to generated audio file or None
        """
        try:
            # For now, create a silent audio file as placeholder
            # In production, you could use system TTS or other services
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            audio_filename = f"fallback_speech_{timestamp}.mp3"
            audio_path = self.temp_dir / audio_filename

            # Create a silent audio clip (placeholder)
            duration = max(3.0, len(text.split()) * 0.5)  # Estimate duration
            silent_clip = AudioFileClip(None).set_duration(duration)

            # Note: This is a placeholder - in production you'd implement actual TTS
            logger.warning(f"Using fallback silent audio for: {text[:50]}...")
            return None  # Return None to indicate no audio

        except Exception as e:
            logger.error(f"Error in fallback speech generation: {e}")
            return None

    def parse_color(self, color_str: str) -> Tuple[int, int, int]:
        """Parse color string (hex or rgba) to RGB tuple"""
        if not color_str:
            return (255, 255, 255)  # Default white

        color_str = str(color_str).strip()

        if color_str.startswith('rgba('):
            # Parse RGBA format: rgba(r,g,b,a)
            rgba_str = color_str.replace('rgba(', '').replace(')', '')
            values = [v.strip() for v in rgba_str.split(',')]
            if len(values) >= 3:
                r, g, b = int(values[0]), int(values[1]), int(values[2])
                return (r, g, b)
        elif color_str.startswith('#'):
            # Parse hex format: #rrggbb
            return self.hex_to_rgb(color_str)
        else:
            # Try to parse as hex without #
            try:
                return self.hex_to_rgb('#' + color_str)
            except:
                logger.warning(f"Could not parse color '{color_str}', using white")
                return (255, 255, 255)

        return (255, 255, 255)  # Default fallback

    def hex_to_rgb(self, hex_color: str) -> Tuple[int, int, int]:
        """Convert hex color to RGB tuple"""
        hex_color = hex_color.lstrip('#')
        if len(hex_color) == 6:
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        return (255, 255, 255)  # Default white

    def parse_dimension(self, dimension_str: str, canvas_width: int, canvas_height: int) -> int:
        """Parse dimension string (vmin, px, %, etc.) to pixel value"""
        if isinstance(dimension_str, (int, float)):
            return int(dimension_str)

        dimension_str = str(dimension_str).strip().lower()

        if 'vmin' in dimension_str:
            # vmin = 1% of viewport minimum dimension
            value = float(dimension_str.replace('vmin', '').strip())
            min_dimension = min(canvas_width, canvas_height)
            return int(min_dimension * value / 100)
        elif 'px' in dimension_str:
            # Direct pixel value
            value = float(dimension_str.replace('px', '').strip())
            return int(value)
        elif '%' in dimension_str:
            # Percentage of canvas width (for width) or height (for height)
            value = float(dimension_str.replace('%', '').strip())
            return int(canvas_width * value / 100)
        else:
            # Try to parse as number (assume pixels)
            try:
                return int(float(dimension_str))
            except ValueError:
                logger.warning(f"Could not parse dimension '{dimension_str}', using default 100px")
                return 100

    def parse_position(self, position_str: str, canvas_dimension: int, element_dimension: int) -> int:
        """Parse position string to pixel value"""
        if isinstance(position_str, (int, float)):
            return int(position_str)

        position_str = str(position_str).strip().lower()

        if '%' in position_str:
            # Percentage of canvas dimension
            value = float(position_str.replace('%', '').strip())
            return int(canvas_dimension * value / 100) - element_dimension // 2
        elif 'px' in position_str:
            # Direct pixel value
            value = float(position_str.replace('px', '').strip())
            return int(value)
        elif 'vmin' in position_str:
            # vmin positioning
            value = float(position_str.replace('vmin', '').strip())
            return int(canvas_dimension * value / 100)
        else:
            # Try to parse as number (assume pixels)
            try:
                return int(float(position_str))
            except ValueError:
                logger.warning(f"Could not parse position '{position_str}', using center")
                return canvas_dimension // 2 - element_dimension // 2

    async def process_template(self, template_path: str, output_filename: str = None) -> str:
        """
        Process a complete video template

        Args:
            template_path: Path to the template JSON file
            output_filename: Optional output filename

        Returns:
            Path to the generated video file
        """
        logger.info(f"🎬 Processing template: {template_path}")

        # Load template
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
        except Exception as e:
            logger.error(f"Failed to load template: {e}")
            raise

        # Validate template
        if not self.validate_template(template_data):
            raise ValueError("Template validation failed")

        # Extract template properties
        width = template_data.get('width', 720)
        height = template_data.get('height', 1280)
        output_format = template_data.get('output_format', 'mp4')

        if not output_filename:
            template_name = Path(template_path).stem
            output_filename = f"{template_name}_unified_output.{output_format}"

        output_path = self.output_dir / output_filename

        # Pre-process template (generate audio, prepare assets)
        processed_template = await self.preprocess_template(template_data)

        # Process all scenes/compositions
        scene_clips = []
        total_duration = 0

        for element in processed_template.get('elements', []):
            if element.get('type') == 'composition':
                scene_clip = await self.process_scene(element, width, height)
                if scene_clip:
                    scene_clips.append(scene_clip)
                    total_duration += scene_clip.duration
                    logger.info(f"✅ Processed scene: {element.get('name', 'Unknown')} - Duration: {scene_clip.duration:.2f}s")

        if not scene_clips:
            raise ValueError("No valid scenes found in template")

        # Concatenate all scenes
        logger.info(f"🔗 Concatenating {len(scene_clips)} scenes...")
        final_video = concatenate_videoclips(scene_clips, method="compose")

        # Write final video
        logger.info(f"💾 Writing final video to {output_path}...")
        final_video.write_videofile(
            str(output_path),
            fps=24,
            codec='libx264',
            audio_codec='aac',
            temp_audiofile='temp-audio.m4a',
            remove_temp=True,
            verbose=False,
            logger=None
        )

        # Cleanup
        final_video.close()
        for clip in scene_clips:
            clip.close()

        logger.info(f"🎉 Video processing complete: {output_path}")
        logger.info(f"📊 Total duration: {total_duration:.2f}s")

        return str(output_path)

    def validate_template(self, template_data: Dict) -> bool:
        """Validate template structure"""
        try:
            # Basic validation
            if not isinstance(template_data, dict):
                logger.error("Template must be a dictionary")
                return False

            if 'elements' not in template_data:
                logger.error("Template must have 'elements' key")
                return False

            elements = template_data['elements']
            if not isinstance(elements, list):
                logger.error("Template 'elements' must be a list")
                return False

            # Check for at least one composition
            compositions = [e for e in elements if e.get('type') == 'composition']
            if not compositions:
                logger.error("Template must have at least one composition element")
                return False

            logger.info("✅ Template validation passed")
            return True

        except Exception as e:
            logger.error(f"Template validation error: {e}")
            return False

    async def preprocess_template(self, template_data: Dict) -> Dict:
        """
        Preprocess template to generate audio and prepare assets

        Args:
            template_data: Template data dictionary

        Returns:
            Processed template data with audio paths
        """
        logger.info("🔄 Preprocessing template...")

        processed_template = template_data.copy()

        # Process each element recursively
        await self._preprocess_elements(processed_template.get('elements', []))

        return processed_template

    async def _preprocess_elements(self, elements: List[Dict]):
        """Recursively preprocess elements to generate audio"""
        for element in elements:
            element_type = element.get('type')

            if element_type == 'composition':
                # Process nested elements
                nested_elements = element.get('elements', [])
                await self._preprocess_elements(nested_elements)

            elif element_type == 'audio':
                # Generate audio if needed
                await self._preprocess_audio_element(element)

            elif element_type == 'text':
                # Check if text needs audio generation
                if element.get('generate_audio', False):
                    await self._preprocess_text_audio(element)

    async def _preprocess_audio_element(self, audio_element: Dict):
        """Preprocess audio element to generate speech if needed"""
        source = audio_element.get('source', '')

        # If source is a text or needs generation
        if not source or not os.path.exists(source):
            # Check for text content
            text_content = audio_element.get('text', audio_element.get('transcript', ''))

            if text_content:
                logger.info(f"🎤 Generating speech for audio element: {text_content[:50]}...")

                # Generate speech using Runway API
                audio_path = await self.generate_speech_runway(text_content)

                if audio_path:
                    audio_element['source'] = audio_path
                    audio_element['generated'] = True
                    logger.info(f"✅ Audio generated: {audio_path}")
                else:
                    # Fallback
                    fallback_path = self.generate_speech_fallback(text_content)
                    if fallback_path:
                        audio_element['source'] = fallback_path
                        audio_element['generated'] = True

    async def _preprocess_text_audio(self, text_element: Dict):
        """Generate audio for text element if requested"""
        text_content = text_element.get('text', '')

        if text_content:
            logger.info(f"🎤 Generating speech for text element: {text_content[:50]}...")

            audio_path = await self.generate_speech_runway(text_content)

            if audio_path:
                text_element['audio_source'] = audio_path
                text_element['has_audio'] = True
                logger.info(f"✅ Text audio generated: {audio_path}")

    async def process_scene(self, scene_element: Dict, width: int, height: int) -> Optional[VideoFileClip]:
        """
        Process a scene/composition element

        Args:
            scene_element: Scene element data
            width: Canvas width
            height: Canvas height

        Returns:
            Composed video clip or None
        """
        scene_name = scene_element.get('name', 'Unknown Scene')
        logger.info(f"🎬 Processing scene: {scene_name}")

        # Get scene duration
        scene_duration = scene_element.get('duration', 3.0)
        elements = scene_element.get('elements', [])

        if not elements:
            logger.warning(f"Scene '{scene_name}' has no elements")
            return None

        # Separate elements by type
        image_elements = [e for e in elements if e.get('type') == 'image']
        text_elements = [e for e in elements if e.get('type') == 'text']
        audio_elements = [e for e in elements if e.get('type') == 'audio']
        shape_elements = [e for e in elements if e.get('type') == 'shape']
        composition_elements = [e for e in elements if e.get('type') == 'composition']

        # Create background clip
        background_clip = self.create_background_clip(image_elements, width, height, scene_duration)

        # Create text overlays
        text_clips = []
        for text_element in text_elements:
            text_clip = self.create_text_clip(text_element, width, height, scene_duration)
            if text_clip:
                text_clips.append(text_clip)

        # Create shape overlays
        shape_clips = []
        for shape_element in shape_elements:
            shape_clip = self.create_shape_clip(shape_element, width, height, scene_duration)
            if shape_clip:
                shape_clips.append(shape_clip)

        # Process nested compositions recursively
        composition_clips = []
        for composition_element in composition_elements:
            composition_clip = await self.process_scene(composition_element, width, height)
            if composition_clip:
                composition_clips.append(composition_clip)

        # Process audio
        audio_clip = None
        if audio_elements:
            audio_clip = self.create_audio_clip(audio_elements[0])  # Use first audio element

        # Composite all elements
        video_clips = [background_clip] + shape_clips + text_clips + composition_clips

        # Filter out None clips
        video_clips = [clip for clip in video_clips if clip is not None]

        if not video_clips:
            logger.warning(f"No valid clips in scene '{scene_name}'")
            return None

        # Create composite
        if len(video_clips) == 1:
            composite_clip = video_clips[0]
        else:
            composite_clip = CompositeVideoClip(video_clips, size=(width, height))

        # Set duration
        composite_clip = composite_clip.set_duration(scene_duration)

        # Add audio if available
        if audio_clip:
            composite_clip = composite_clip.set_audio(audio_clip)

        logger.info(f"✅ Scene processed: {scene_name} - Duration: {scene_duration}s")
        return composite_clip

    def create_background_clip(self, image_elements: List[Dict], width: int, height: int, duration: float) -> VideoFileClip:
        """Create background clip from image elements or solid color"""

        if image_elements:
            # Use first image element as background
            image_element = image_elements[0]
            image_path = self.resolve_image_source(image_element.get('source', ''))

            if image_path and image_path.exists():
                logger.info(f"📸 Using background image: {image_path}")

                # Load and resize image
                img = Image.open(image_path)
                img = img.resize((width, height), Image.Resampling.LANCZOS)

                # Apply color overlay if specified
                color_overlay = image_element.get('color_overlay')
                if color_overlay:
                    overlay_color = self.parse_color(color_overlay)
                    overlay = Image.new('RGBA', (width, height), overlay_color + (int(255 * 0.3),))
                    img = Image.alpha_composite(img.convert('RGBA'), overlay)

                # Convert to numpy array
                img_array = np.array(img.convert('RGB'))

                # Create clip
                background_clip = ImageClip(img_array, duration=duration)

                # Apply animations if specified
                animations = image_element.get('animations', [])
                if animations:
                    background_clip = self.apply_image_animations(background_clip, animations, width, height)

                return background_clip

        # Fallback: create solid color background
        logger.info("🎨 Creating solid color background")
        background_color = self.parse_color(self.default_background_color)
        background_clip = ColorClip(size=(width, height), color=background_color, duration=duration)

        return background_clip

    def resolve_image_source(self, source: str) -> Optional[Path]:
        """
        Resolve image source to actual file path

        Args:
            source: Image source (could be ID, filename, or path)

        Returns:
            Path to image file or None
        """
        if not source:
            return self.get_random_image()

        # Check if source is a direct path
        source_path = Path(source)
        if source_path.exists():
            return source_path

        # Check if source is in images directory
        images_path = self.images_dir / source
        if images_path.exists():
            return images_path

        # Try with common extensions
        for ext in ['.jpg', '.jpeg', '.png', '.bmp']:
            test_path = self.images_dir / f"{source}{ext}"
            if test_path.exists():
                return test_path

        # Try to parse as index
        try:
            index = int(source)
            return self.get_image_by_index(index)
        except ValueError:
            pass

        # Fallback to random image
        logger.warning(f"Could not resolve image source '{source}', using random image")
        return self.get_random_image()

    def apply_image_animations(self, clip: ImageClip, animations: List[Dict], width: int, height: int) -> ImageClip:
        """Apply animations to image clip"""

        for animation in animations:
            anim_type = animation.get('type', '')

            if anim_type == 'pan':
                # Apply pan/zoom animation
                start_scale = self.parse_dimension(animation.get('start_scale', '100%'), width, height) / 100
                end_scale = self.parse_dimension(animation.get('end_scale', '120%'), width, height) / 100

                # Create resize function
                def resize_func(t):
                    progress = t / clip.duration
                    scale = start_scale + (end_scale - start_scale) * progress
                    return scale

                clip = clip.resize(resize_func)

            elif anim_type == 'fade':
                # Apply fade animation
                fade_duration = animation.get('duration', 1.0)
                if animation.get('fade_in', False):
                    clip = clip.fadein(fade_duration)
                if animation.get('fade_out', False):
                    clip = clip.fadeout(fade_duration)

        return clip

    def create_text_clip(self, text_element: Dict, width: int, height: int, duration: float) -> Optional[TextClip]:
        """Create text clip from text element"""

        text_content = text_element.get('text', 'Sample Text')
        if not text_content:
            return None

        # Parse text properties
        font_family = text_element.get('font_family', 'Arial')
        font_size_str = text_element.get('font_size', '24px')
        font_size = self.parse_dimension(font_size_str, width, height)

        fill_color = text_element.get('fill_color', self.default_font_color)
        stroke_color = text_element.get('stroke_color', '#000000')

        # Parse colors
        fill_rgb = self.parse_color(fill_color)
        stroke_rgb = self.parse_color(stroke_color)

        logger.info(f"📝 Creating text clip: {text_content[:30]}...")

        try:
            # Create text clip using MoviePy
            text_clip = TextClip(
                text_content,
                fontsize=font_size,
                color=fill_rgb,
                font=font_family,
                stroke_color=stroke_rgb,
                stroke_width=1
            ).set_duration(duration)

            # Position text
            x_pos = self.parse_position(text_element.get('x', '50%'), width, 0)
            y_pos = self.parse_position(text_element.get('y', '50%'), height, 0)

            text_clip = text_clip.set_position((x_pos, y_pos))

            # Apply text animations if specified
            animations = text_element.get('animations', [])
            if animations:
                text_clip = self.apply_text_animations(text_clip, animations)

            logger.info(f"✅ Text clip created at position ({x_pos}, {y_pos})")
            return text_clip

        except Exception as e:
            logger.error(f"Failed to create text clip: {e}")
            return None

    def apply_text_animations(self, clip: TextClip, animations: List[Dict]) -> TextClip:
        """Apply animations to text clip"""

        for animation in animations:
            anim_type = animation.get('type', '')

            if anim_type == 'fade':
                fade_duration = animation.get('duration', 1.0)
                if animation.get('fade_in', False):
                    clip = clip.fadein(fade_duration)
                if animation.get('fade_out', False):
                    clip = clip.fadeout(fade_duration)

            elif anim_type == 'slide':
                # Implement slide animation
                direction = animation.get('direction', 'up')
                # This would need more complex implementation
                pass

        return clip

    def create_shape_clip(self, shape_element: Dict, width: int, height: int, duration: float) -> Optional[ImageClip]:
        """Create shape overlay clip"""

        logger.info(f"🔷 Creating shape clip: {shape_element.get('id', 'unknown')}")

        # Extract shape properties
        fill_color = shape_element.get('fill_color', '#ffffff')
        shape_width = shape_element.get('width', '100px')
        shape_height = shape_element.get('height', '100px')

        # Parse dimensions
        shape_width_px = self.parse_dimension(shape_width, width, height)
        shape_height_px = self.parse_dimension(shape_height, width, height)

        # Create shape image
        shape_image = self.create_shape_image(
            shape_element, shape_width_px, shape_height_px, fill_color
        )

        if shape_image is None:
            return None

        # Create clip
        shape_clip = ImageClip(shape_image, duration=duration)

        # Position shape
        x_pos = self.parse_position(shape_element.get('x', '50%'), width, shape_width_px)
        y_pos = self.parse_position(shape_element.get('y', '50%'), height, shape_height_px)

        shape_clip = shape_clip.set_position((x_pos, y_pos))

        logger.info(f"✅ Shape clip created at position ({x_pos}, {y_pos})")
        return shape_clip

    def create_shape_image(self, shape_element: Dict, width: int, height: int, fill_color: str) -> Optional[np.ndarray]:
        """Create shape image"""

        try:
            # Parse fill color
            fill_rgb = self.parse_color(fill_color)

            # Create image with transparent background
            img = Image.new('RGBA', (width, height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(img)

            # Get shape path or create basic shape
            path = shape_element.get('path')
            shape_type = shape_element.get('shape_type', 'rectangle')

            if path:
                # For complex paths, create a rectangle for now
                # In a full implementation, you'd parse SVG paths
                draw.rectangle((0, 0, width, height), fill=fill_rgb + (255,))
            elif shape_type == 'circle':
                # Create circle
                draw.ellipse((0, 0, width, height), fill=fill_rgb + (255,))
            elif shape_type == 'ellipse':
                # Create ellipse
                draw.ellipse((0, 0, width, height), fill=fill_rgb + (255,))
            else:
                # Default rectangle
                draw.rectangle((0, 0, width, height), fill=fill_rgb + (255,))

            return np.array(img)

        except Exception as e:
            logger.error(f"Failed to create shape image: {e}")
            return None

    def create_audio_clip(self, audio_element: Dict) -> Optional[AudioFileClip]:
        """Create audio clip from audio element"""

        source = audio_element.get('source', '')

        if not source or not os.path.exists(source):
            logger.warning(f"Audio source not found: {source}")
            return None

        try:
            logger.info(f"🎵 Loading audio: {source}")
            audio_clip = AudioFileClip(source)

            # Apply audio properties
            volume = audio_element.get('volume', 1.0)
            if volume != 1.0:
                audio_clip = audio_clip.volumex(volume)

            # Apply fade effects
            fade_in = audio_element.get('audio_fade_in', 0)
            fade_out = audio_element.get('audio_fade_out', 0)

            if fade_in > 0:
                audio_clip = audio_clip.fadein(fade_in)
            if fade_out > 0:
                audio_clip = audio_clip.fadeout(fade_out)

            logger.info(f"✅ Audio clip loaded: {audio_clip.duration:.2f}s")
            return audio_clip

        except Exception as e:
            logger.error(f"Failed to load audio: {e}")
            return None

    def cleanup(self):
        """Clean up temporary files"""
        try:
            # Clean up temporary audio files
            for temp_file in self.temp_dir.glob("*.mp3"):
                temp_file.unlink()
            for temp_file in self.temp_dir.glob("*.wav"):
                temp_file.unlink()
            logger.info("🧹 Temporary files cleaned up")
        except Exception as e:
            logger.warning(f"Failed to clean up temporary files: {e}")


# Batch processing functions
async def process_all_templates(processor: UnifiedVideoProcessor, templates_dir: str = "templates"):
    """Process all templates in the templates directory"""

    templates_path = Path(templates_dir)
    if not templates_path.exists():
        logger.error(f"Templates directory not found: {templates_path}")
        return

    template_files = list(templates_path.glob("*.json"))
    logger.info(f"🎬 Found {len(template_files)} templates to process")

    results = []

    for template_file in template_files:
        try:
            logger.info(f"\n{'='*60}")
            logger.info(f"🎯 Processing template: {template_file.name}")
            logger.info(f"{'='*60}")

            output_path = await processor.process_template(str(template_file))
            results.append({
                'template': template_file.name,
                'status': 'success',
                'output': output_path
            })

            logger.info(f"✅ Successfully processed: {template_file.name}")

        except Exception as e:
            logger.error(f"❌ Failed to process {template_file.name}: {e}")
            results.append({
                'template': template_file.name,
                'status': 'failed',
                'error': str(e)
            })

    # Print summary
    logger.info(f"\n{'='*60}")
    logger.info(f"📊 PROCESSING SUMMARY")
    logger.info(f"{'='*60}")

    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] == 'failed']

    logger.info(f"✅ Successful: {len(successful)}")
    logger.info(f"❌ Failed: {len(failed)}")

    if successful:
        logger.info(f"\n🎉 Successfully processed templates:")
        for result in successful:
            logger.info(f"  - {result['template']} → {result['output']}")

    if failed:
        logger.info(f"\n💥 Failed templates:")
        for result in failed:
            logger.info(f"  - {result['template']}: {result['error']}")

    return results


async def main():
    """Main execution function"""
    import argparse

    parser = argparse.ArgumentParser(description='Unified Video Processor for VisionFrame AI')
    parser.add_argument('template', nargs='?', help='Template file to process')
    parser.add_argument('--output', '-o', help='Output filename')
    parser.add_argument('--images-dir', default=r"C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140",
                       help='Directory containing images')
    parser.add_argument('--runway-key', help='Runway API key for speech generation')
    parser.add_argument('--output-dir', default='unified_outputs', help='Output directory')
    parser.add_argument('--batch', action='store_true', help='Process all templates in templates directory')
    parser.add_argument('--templates-dir', default='templates', help='Templates directory for batch processing')

    args = parser.parse_args()

    # Initialize processor
    processor = UnifiedVideoProcessor(
        images_dir=args.images_dir,
        runway_api_key=args.runway_key,
        output_dir=args.output_dir
    )

    try:
        if args.batch:
            # Batch process all templates
            logger.info("🚀 Starting batch processing of all templates...")
            await process_all_templates(processor, args.templates_dir)

        elif args.template:
            # Process single template
            logger.info(f"🚀 Processing single template: {args.template}")
            output_path = await processor.process_template(args.template, args.output)
            logger.info(f"🎉 Video created successfully: {output_path}")

        else:
            # No template specified, show help
            parser.print_help()
            return

    except Exception as e:
        logger.error(f"💥 Processing failed: {e}")
        raise

    finally:
        # Cleanup
        processor.cleanup()


if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())