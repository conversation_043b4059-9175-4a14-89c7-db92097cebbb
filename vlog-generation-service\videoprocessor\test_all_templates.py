#!/usr/bin/env python3
"""
Template Testing Script
Tests all templates in the templates folder to ensure they can be rendered successfully.
"""

import os
import sys
import json
import logging
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import VisionFrameVideoProcessor

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('template_testing.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TemplateTestRunner:
    """Test runner for video templates"""
    
    def __init__(self):
        self.templates_dir = Path("templates")
        self.output_dir = Path("template_test_outputs")
        self.output_dir.mkdir(exist_ok=True)
        
        # Initialize processor
        elevenlabs_key = os.getenv('ELEVENLABS_API_KEY')
        self.processor = VisionFrameVideoProcessor(elevenlabs_key, str(self.output_dir))
        
        self.test_results = {}
        
    def get_template_files(self):
        """Get all JSON template files"""
        if not self.templates_dir.exists():
            logger.error(f"Templates directory not found: {self.templates_dir}")
            return []
            
        template_files = list(self.templates_dir.glob("*.json"))
        logger.info(f"Found {len(template_files)} template files")
        return template_files
    
    def validate_template_structure(self, template_path: Path) -> tuple[bool, str, dict]:
        """Validate template JSON structure"""
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                template_data = json.load(f)
            
            # Check required fields
            required_fields = ['output_format', 'width', 'height', 'elements']
            missing_fields = [field for field in required_fields if field not in template_data]
            
            if missing_fields:
                return False, f"Missing required fields: {missing_fields}", {}
            
            # Check if elements exist
            if not template_data.get('elements'):
                return False, "No elements found in template", {}
            
            # Basic structure validation
            elements = template_data['elements']
            if not isinstance(elements, list):
                return False, "Elements must be a list", {}
            
            return True, "Template structure valid", template_data
            
        except json.JSONDecodeError as e:
            return False, f"Invalid JSON: {e}", {}
        except Exception as e:
            return False, f"Error reading template: {e}", {}
    
    def analyze_template_complexity(self, template_data: dict) -> dict:
        """Analyze template complexity and potential issues"""
        analysis = {
            'total_elements': len(template_data.get('elements', [])),
            'element_types': {},
            'has_audio': False,
            'has_images': False,
            'has_text': False,
            'has_animations': False,
            'has_compositions': False,
            'aspect_ratio': f"{template_data.get('width', 0)}x{template_data.get('height', 0)}",
            'duration': template_data.get('duration', 'auto'),
            'potential_issues': []
        }
        
        def analyze_element(element, parent_name="root"):
            element_type = element.get('type', 'unknown')
            analysis['element_types'][element_type] = analysis['element_types'].get(element_type, 0) + 1
            
            if element_type == 'audio':
                analysis['has_audio'] = True
            elif element_type == 'image':
                analysis['has_images'] = True
            elif element_type == 'text':
                analysis['has_text'] = True
            elif element_type == 'composition':
                analysis['has_compositions'] = True
                # Recursively analyze composition elements
                for sub_element in element.get('elements', []):
                    analyze_element(sub_element, f"{parent_name}.{element.get('name', 'comp')}")
            
            # Check for animations
            if element.get('animations'):
                analysis['has_animations'] = True
            
            # Check for potential issues
            if element_type == 'image' and element.get('source'):
                source = element['source']
                if len(source) == 36 and '-' in source:  # Looks like UUID
                    analysis['potential_issues'].append(f"UUID image source: {source[:8]}...")
            
            if element_type == 'text':
                font_family = element.get('font_family')
                if font_family and font_family not in ['Arial', 'Times New Roman', 'Helvetica']:
                    analysis['potential_issues'].append(f"Custom font: {font_family}")
        
        for element in template_data.get('elements', []):
            analyze_element(element)
        
        return analysis
    
    def test_template(self, template_path: Path) -> dict:
        """Test a single template"""
        template_name = template_path.stem
        logger.info(f"\n{'='*60}")
        logger.info(f"Testing template: {template_name}")
        logger.info(f"{'='*60}")
        
        result = {
            'template_name': template_name,
            'template_path': str(template_path),
            'timestamp': datetime.now().isoformat(),
            'validation_passed': False,
            'processing_passed': False,
            'output_file': None,
            'error_message': None,
            'analysis': {},
            'processing_time': 0
        }
        
        # Step 1: Validate template structure
        logger.info("Step 1: Validating template structure...")
        is_valid, validation_msg, template_data = self.validate_template_structure(template_path)
        result['validation_passed'] = is_valid
        
        if not is_valid:
            result['error_message'] = f"Validation failed: {validation_msg}"
            logger.error(result['error_message'])
            return result
        
        logger.info(f"✅ Template validation passed: {validation_msg}")
        
        # Step 2: Analyze template complexity
        logger.info("Step 2: Analyzing template complexity...")
        analysis = self.analyze_template_complexity(template_data)
        result['analysis'] = analysis
        
        logger.info(f"📊 Template Analysis:")
        logger.info(f"   - Total elements: {analysis['total_elements']}")
        logger.info(f"   - Element types: {analysis['element_types']}")
        logger.info(f"   - Aspect ratio: {analysis['aspect_ratio']}")
        logger.info(f"   - Has audio: {analysis['has_audio']}")
        logger.info(f"   - Has images: {analysis['has_images']}")
        logger.info(f"   - Has animations: {analysis['has_animations']}")
        
        if analysis['potential_issues']:
            logger.warning(f"⚠️  Potential issues: {analysis['potential_issues']}")
        
        # Step 3: Process template
        logger.info("Step 3: Processing template...")
        output_filename = f"{template_name}_test_output.mp4"
        
        try:
            start_time = datetime.now()
            output_path = self.processor.process_template(str(template_path), output_filename)
            end_time = datetime.now()
            
            processing_time = (end_time - start_time).total_seconds()
            result['processing_time'] = processing_time
            result['processing_passed'] = True
            result['output_file'] = output_path
            
            logger.info(f"✅ Template processing completed successfully!")
            logger.info(f"📁 Output file: {output_path}")
            logger.info(f"⏱️  Processing time: {processing_time:.2f} seconds")
            
        except Exception as e:
            result['error_message'] = f"Processing failed: {str(e)}"
            logger.error(f"❌ Template processing failed: {e}")
        
        return result
    
    def run_all_tests(self):
        """Run tests on all templates"""
        logger.info("🚀 Starting template testing...")
        
        template_files = self.get_template_files()
        if not template_files:
            logger.error("No template files found!")
            return
        
        total_templates = len(template_files)
        successful_tests = 0
        
        for i, template_path in enumerate(template_files, 1):
            logger.info(f"\n📋 Testing template {i}/{total_templates}")
            
            result = self.test_template(template_path)
            self.test_results[result['template_name']] = result
            
            if result['processing_passed']:
                successful_tests += 1
        
        # Generate summary report
        self.generate_summary_report(total_templates, successful_tests)
    
    def generate_summary_report(self, total_templates: int, successful_tests: int):
        """Generate a summary report of all tests"""
        logger.info(f"\n{'='*80}")
        logger.info(f"TEMPLATE TESTING SUMMARY")
        logger.info(f"{'='*80}")
        
        logger.info(f"📊 Total templates tested: {total_templates}")
        logger.info(f"✅ Successful: {successful_tests}")
        logger.info(f"❌ Failed: {total_templates - successful_tests}")
        logger.info(f"📈 Success rate: {(successful_tests/total_templates)*100:.1f}%")
        
        # Detailed results
        logger.info(f"\n📋 DETAILED RESULTS:")
        for template_name, result in self.test_results.items():
            status = "✅ PASS" if result['processing_passed'] else "❌ FAIL"
            processing_time = result.get('processing_time', 0)
            logger.info(f"   {status} {template_name:<30} ({processing_time:.1f}s)")
            
            if not result['processing_passed'] and result['error_message']:
                logger.info(f"      Error: {result['error_message']}")
        
        # Save detailed report to JSON
        report_file = self.output_dir / "template_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"\n📄 Detailed report saved to: {report_file}")
        logger.info(f"📁 Test outputs saved to: {self.output_dir}")

if __name__ == "__main__":
    runner = TemplateTestRunner()
    runner.run_all_tests()
