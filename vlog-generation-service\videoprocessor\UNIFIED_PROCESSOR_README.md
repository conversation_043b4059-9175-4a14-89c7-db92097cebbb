# Unified Video Processor for VisionFrame AI

A comprehensive, single-script solution for processing all video templates with automatic image sourcing and Runway API speech generation.

## 🌟 Features

### ✅ **Template-Agnostic Processing**
- **Single script handles all templates** - No need for separate processors
- **Automatic element detection** - Supports text, image, shape, audio, and composition elements
- **Recursive composition processing** - Handles nested scenes and complex structures
- **Flexible template validation** - Robust error handling and validation

### 🖼️ **Automatic Image Sourcing**
- **Configurable image directory** - Uses `C:\Users\<USER>\Documents\GitHub\mexcpumpbot\ContentAutomation\ContentAutomation\vlog-generation-service\content-images\140` by default
- **Smart image resolution** - Handles filenames, indices, IDs, and random selection
- **Multiple format support** - JPG, PNG, BMP, TIFF, WebP
- **Fallback mechanisms** - Always provides an image even if source is missing

### 🎤 **Runway API Speech Generation**
- **Automatic speech generation** - Converts text to speech using Runway API
- **Fallback support** - Graceful handling when API is unavailable
- **Audio preprocessing** - Generates audio before video processing
- **Multiple voice support** - Configurable voice selection

### 🎨 **Comprehensive Element Support**
- **Text elements** - Full typography support with animations
- **Image elements** - Background images with overlays and animations
- **Shape elements** - Rectangles, circles, ellipses with custom colors
- **Audio elements** - Speech generation and audio file loading
- **Composition elements** - Recursive scene processing

### 🔧 **Advanced Processing**
- **Color parsing** - Supports hex (#ffffff) and RGBA (rgba(255,255,255,1)) formats
- **Dimension parsing** - Handles px, %, vmin units with responsive scaling
- **Position parsing** - Flexible positioning with multiple unit types
- **Animation support** - Pan, zoom, fade effects

## 🚀 Quick Start

### Basic Usage

```bash
# Process a single template
python unified_video_processor.py "templates/chat promotion alternative.json"

# Process with custom output name
python unified_video_processor.py "templates/slideshow.json" --output "my_video.mp4"

# Process all templates (batch mode)
python unified_video_processor.py --batch

# Use custom image directory
python unified_video_processor.py "template.json" --images-dir "/path/to/images"

# Use Runway API for speech generation
python unified_video_processor.py "template.json" --runway-key "your_api_key"
```

### Programmatic Usage

```python
import asyncio
from unified_video_processor import UnifiedVideoProcessor

async def process_video():
    # Initialize processor
    processor = UnifiedVideoProcessor(
        images_dir="path/to/images",
        runway_api_key="your_runway_key",
        output_dir="outputs"
    )
    
    # Process template
    output_path = await processor.process_template("template.json")
    print(f"Video created: {output_path}")
    
    # Cleanup
    processor.cleanup()

# Run
asyncio.run(process_video())
```

## 📁 Directory Structure

```
videoprocessor/
├── unified_video_processor.py      # Main unified processor
├── test_unified_processor.py       # Test suite
├── templates/                      # Template JSON files
│   ├── chat promotion alternative.json
│   ├── image slideshow with intro and outro.json
│   └── ...
├── unified_outputs/               # Generated videos
└── temp/                         # Temporary audio files
```

## 🎯 Template Support

The unified processor supports all existing template types:

- ✅ **Chat Promotion Alternative**
- ✅ **Image Slideshow with Intro and Outro**
- ✅ **Photo Story**
- ✅ **Polaroid Photo Slideshow**
- ✅ **Question and Answer Content**
- ✅ **QA Instagram Story**
- ✅ **Shortform News Story**
- ✅ **Shortform Voice Over**
- ✅ **Shortform Voice Over 2**

## 🔧 Configuration

### Environment Variables

```bash
# Optional: Runway API key for speech generation
export RUNWAY_API_KEY="your_runway_api_key"
```

### Command Line Options

```bash
python unified_video_processor.py --help

options:
  template              Template file to process
  --output, -o         Output filename
  --images-dir         Directory containing images (default: content-images/140)
  --runway-key         Runway API key for speech generation
  --output-dir         Output directory (default: unified_outputs)
  --batch              Process all templates in templates directory
  --templates-dir      Templates directory for batch processing
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_unified_processor.py
```

Tests include:
- ✅ Color parsing (hex, RGBA)
- ✅ Dimension parsing (px, %, vmin)
- ✅ Image resolution and sourcing
- ✅ Single template processing
- ✅ Batch processing

## 🎨 Template Structure

The processor handles templates with this structure:

```json
{
  "width": 720,
  "height": 1280,
  "output_format": "mp4",
  "elements": [
    {
      "type": "composition",
      "name": "Scene 1",
      "duration": 3.0,
      "elements": [
        {
          "type": "image",
          "source": "19.jpg",
          "animations": [{"type": "pan", "start_scale": "100%", "end_scale": "120%"}]
        },
        {
          "type": "text",
          "text": "Hello World",
          "font_size": "24px",
          "fill_color": "#ffffff"
        },
        {
          "type": "shape",
          "fill_color": "rgba(255,0,0,0.5)",
          "width": "100px",
          "height": "50px"
        },
        {
          "type": "audio",
          "text": "This text will be converted to speech"
        }
      ]
    }
  ]
}
```

## 🔄 Processing Pipeline

1. **Template Loading** - Load and validate JSON template
2. **Preprocessing** - Generate audio using Runway API
3. **Scene Processing** - Process each composition recursively
4. **Element Creation** - Create video clips for each element type
5. **Composition** - Composite all elements into scenes
6. **Concatenation** - Combine scenes into final video
7. **Output** - Write final video file
8. **Cleanup** - Remove temporary files

## 🚨 Error Handling

The processor includes comprehensive error handling:

- **Template validation** - Checks structure and required fields
- **Image fallbacks** - Uses random images when sources are missing
- **Audio fallbacks** - Continues without audio if generation fails
- **Color parsing** - Defaults to white for invalid colors
- **Dimension parsing** - Uses sensible defaults for invalid dimensions

## 📊 Performance

- **Memory efficient** - Processes elements sequentially
- **Caching** - Images and fonts are cached for reuse
- **Cleanup** - Automatic temporary file cleanup
- **Logging** - Comprehensive logging for debugging

## 🤝 Integration

The unified processor integrates seamlessly with:

- **Existing templates** - No template modifications required
- **Runway API** - Automatic speech generation
- **Image libraries** - Flexible image sourcing
- **MoviePy** - Professional video processing
- **Async/await** - Modern Python async support

## 📈 Benefits Over Separate Scripts

1. **Maintainability** - Single codebase to maintain
2. **Consistency** - Uniform processing across all templates
3. **Scalability** - Easy to add new template types
4. **Efficiency** - Shared code and optimizations
5. **Testing** - Comprehensive test coverage
6. **Documentation** - Single source of truth
