{"chat promotion alternative": {"template_name": "chat promotion alternative", "template_path": "templates\\chat promotion alternative.json", "timestamp": "2025-05-27T12:58:36.375615", "validation_passed": true, "processing_passed": true, "output_file": "template_test_outputs\\chat promotion alternative_test_output.mp4", "error_message": null, "analysis": {"total_elements": 2, "element_types": {"composition": 17, "shape": 25, "text": 8}, "has_audio": false, "has_images": false, "has_text": true, "has_animations": true, "has_compositions": true, "aspect_ratio": "720x900", "duration": 21, "potential_issues": ["Custom font: <PERSON><PERSON>", "Custom font: Inter"]}, "processing_time": 328.130002}, "image slideshow with intro and outro": {"template_name": "image slideshow with intro and outro", "template_path": "templates\\image slideshow with intro and outro.json", "timestamp": "2025-05-27T13:04:04.516617", "validation_passed": true, "processing_passed": true, "output_file": "template_test_outputs\\image slideshow with intro and outro_test_output.mp4", "error_message": null, "analysis": {"total_elements": 7, "element_types": {"composition": 7, "image": 4, "text": 8, "shape": 1}, "has_audio": false, "has_images": true, "has_text": true, "has_animations": true, "has_compositions": true, "aspect_ratio": "720x1280", "duration": 20.8339, "potential_issues": ["UUID image source: 5bc5ed6f...", "Custom font: <PERSON>", "Custom font: <PERSON>", "Custom font: <PERSON>", "UUID image source: 63dfc7e7...", "Custom font: <PERSON><PERSON> Condensed", "UUID image source: 5e62bfc9...", "Custom font: <PERSON><PERSON> Condensed", "UUID image source: 0ae5625b...", "Custom font: <PERSON><PERSON> Condensed", "Custom font: <PERSON>", "Custom font: <PERSON><PERSON>"]}, "processing_time": 188.698609}, "photo story": {"template_name": "photo story", "template_path": "templates\\photo story.json", "timestamp": "2025-05-27T13:07:13.227225", "validation_passed": true, "processing_passed": true, "output_file": "template_test_outputs\\photo story_test_output.mp4", "error_message": null, "analysis": {"total_elements": 4, "element_types": {"composition": 4, "shape": 4, "text": 4, "image": 3}, "has_audio": false, "has_images": true, "has_text": true, "has_animations": false, "has_compositions": true, "aspect_ratio": "1280x720", "duration": 13.5, "potential_issues": ["Custom font: Inter", "UUID image source: c0346505...", "Custom font: Caveat", "UUID image source: 8c281b0c...", "Custom font: Caveat", "UUID image source: 93bb8d2b...", "Custom font: Caveat"]}, "processing_time": 145.949106}, "polaroid photo slideshow": {"template_name": "polaroid photo slideshow", "template_path": "templates\\polaroid photo slideshow.json", "timestamp": "2025-05-27T13:09:39.181331", "validation_passed": true, "processing_passed": true, "output_file": "template_test_outputs\\polaroid photo slideshow_test_output.mp4", "error_message": null, "analysis": {"total_elements": 2, "element_types": {"composition": 5, "image": 3, "text": 5, "shape": 1}, "has_audio": false, "has_images": true, "has_text": true, "has_animations": true, "has_compositions": true, "aspect_ratio": "1280x720", "duration": 9.15, "potential_issues": ["UUID image source: 098f8689...", "Custom font: Caveat", "UUID image source: 0b3cb194...", "Custom font: Caveat", "UUID image source: b90d9c03...", "Custom font: Caveat", "Custom font: <PERSON><PERSON>", "Custom font: <PERSON><PERSON>"]}, "processing_time": 142.954091}, "QA instagram story": {"template_name": "QA instagram story", "template_path": "templates\\QA instagram story.json", "timestamp": "2025-05-27T13:12:02.139422", "validation_passed": true, "processing_passed": true, "output_file": "template_test_outputs\\QA instagram story_test_output.mp4", "error_message": null, "analysis": {"total_elements": 3, "element_types": {"text": 3, "composition": 1, "shape": 2}, "has_audio": false, "has_images": false, "has_text": true, "has_animations": true, "has_compositions": true, "aspect_ratio": "720x1280", "duration": 11.81, "potential_issues": ["Custom font: <PERSON><PERSON>", "Custom font: <PERSON><PERSON>", "Custom font: <PERSON><PERSON>"]}, "processing_time": 25.808508}, "question and answer content": {"template_name": "question and answer content", "template_path": "templates\\question and answer content.json", "timestamp": "2025-05-27T13:12:27.952929", "validation_passed": true, "processing_passed": false, "output_file": null, "error_message": "Processing failed: No valid scenes found in template", "analysis": {"total_elements": 6, "element_types": {"audio": 1, "video": 1, "text": 3, "shape": 1}, "has_audio": true, "has_images": false, "has_text": true, "has_animations": true, "has_compositions": false, "aspect_ratio": "720x1280", "duration": 12.0332, "potential_issues": ["Custom font: <PERSON><PERSON><PERSON>", "Custom font: <PERSON><PERSON><PERSON>", "Custom font: <PERSON><PERSON><PERSON>"]}, "processing_time": 0}, "shortform news story": {"template_name": "shortform news story", "template_path": "templates\\shortform news story.json", "timestamp": "2025-05-27T13:12:27.958930", "validation_passed": true, "processing_passed": false, "output_file": null, "error_message": "Processing failed: 'list' object has no attribute 'startswith'", "analysis": {"total_elements": 5, "element_types": {"composition": 9, "image": 5, "text": 9, "shape": 3, "audio": 3}, "has_audio": true, "has_images": true, "has_text": true, "has_animations": true, "has_compositions": true, "aspect_ratio": "720x1280", "duration": "auto", "potential_issues": ["UUID image source: 67f6fbce...", "Custom font: <PERSON><PERSON><PERSON>", "Custom font: <PERSON><PERSON><PERSON>", "UUID image source: 94b5cd60...", "Custom font: <PERSON><PERSON>", "Custom font: <PERSON><PERSON>", "UUID image source: 2374fc71...", "Custom font: <PERSON><PERSON>", "Custom font: <PERSON><PERSON>", "UUID image source: 13b652e0...", "Custom font: <PERSON><PERSON>", "Custom font: <PERSON><PERSON>", "UUID image source: 67f6fbce...", "Custom font: <PERSON><PERSON><PERSON>"]}, "processing_time": 0}, "shortfrom voice over 2": {"template_name": "shortfrom voice over 2", "template_path": "templates\\shortfrom voice over 2.json", "timestamp": "2025-05-27T13:12:28.624930", "validation_passed": false, "processing_passed": false, "output_file": null, "error_message": "Validation failed: Invalid JSON: Expecting property name enclosed in double quotes: line 2 column 3 (char 4)", "analysis": {}, "processing_time": 0}, "shortfrom voice over": {"template_name": "shortfrom voice over", "template_path": "templates\\shortfrom voice over.json", "timestamp": "2025-05-27T13:12:28.626930", "validation_passed": true, "processing_passed": true, "output_file": "template_test_outputs\\shortfrom voice over_test_output.mp4", "error_message": null, "analysis": {"total_elements": 4, "element_types": {"composition": 4, "image": 4, "text": 4, "audio": 4}, "has_audio": true, "has_images": true, "has_text": true, "has_animations": true, "has_compositions": true, "aspect_ratio": "720x1280", "duration": "auto", "potential_issues": ["UUID image source: 99bac41d...", "Custom font: <PERSON><PERSON><PERSON>", "UUID image source: 91ae53bc...", "Custom font: <PERSON><PERSON><PERSON>", "UUID image source: 6fe2f0b2...", "Custom font: <PERSON><PERSON><PERSON>", "UUID image source: 62879b23...", "Custom font: <PERSON><PERSON><PERSON>"]}, "processing_time": 251.485838}}